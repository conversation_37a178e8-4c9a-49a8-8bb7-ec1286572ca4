import 'package:flutter/material.dart';

class AppPallete {
  static const Color lightGreyForText = Color(0xFF878787);
  static const Color blackForText = Colors.black;
  static const Color lightBlueForText = Color(0xFF004CFF);
  static const Color darkGreyForText = Color(0xFF878787);
  static const Color binkForText = Color(0xFFF83758);
  static const Color lightGrey = Color(0xFF8F959E);
  static const Color lightBlack = Color(0xFF434242);
  static const Color lightOrange = Color(0xFFF68B3B);
  static const Color primaryColor = Color(0xFFFDB022);
  static const Color borderColor = Color(0xFFE8ECF4);
  static const Color white = Colors.white;
  static const Color redColor = Colors.red;
  static const Color blueColor = Colors.blue;
  static const Color orangeColor = Colors.orange;
  static const Color greenColor = Colors.green;
  static const Color greyColor = Colors.grey;
  static const Color whiteColor = Colors.white;
  static const Color secondary = Colors.lightBlueAccent;
  static const Color processing = Colors.yellow;
  static const Color pending = Colors.blueGrey;
  static const Color shipped = Colors.greenAccent;
  static const Color background = Colors.white;

}
