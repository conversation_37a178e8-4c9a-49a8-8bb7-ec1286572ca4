
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fl_chart/fl_chart.dart';
import '../../../../core/theme/app_pallete.dart';
import '../../../../core/theme/font_weight_helper.dart';
import '../../../../core/theme/values_manager.dart';
import '../../domain/entitise/order_chart_data.dart';
import 'dashboard_empty_state.dart';
import 'dashboard_loading_shimmer.dart';

class OrdersChartWidget extends StatelessWidget {
  final OrderChartData? chartData;
  final bool isLoading;
  final String? error;
  final VoidCallback? onRetry;

  const OrdersChartWidget({
    Key? key,
    this.chartData,
    required this.isLoading,
    this.error,
    this.onRetry,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (isLoading && chartData == null) {
      return const DashboardLoadingShimmer();
    }

    if (error != null) {
      return DashboardEmptyState(
        title: 'حدث خطأ',
        message: error!,
        icon: Icons.error_outline,
        onRetry: onRetry,
      );
    }

    if (chartData == null || chartData!.monthlySales.isEmpty) {
      return DashboardEmptyState(
        title: 'لا توجد بيانات',
        message: 'سيتم عرض الرسم البياني هنا عند استلام الطلبات',
        icon: Icons.show_chart,
        onRetry: onRetry,
      );
    }

    return _ChartContent(chartData: chartData!);
  }
}

class _ChartContent extends StatelessWidget {
  final OrderChartData chartData;

  const _ChartContent({required this.chartData});

  @override
  Widget build(BuildContext context) {
    final maxValue = chartData.monthlySales
        .map((item) => item.value)
        .reduce((a, b) => a > b ? a : b);

    return Container(
      padding: EdgeInsets.all(ValuesManager.paddingMedium),
      decoration: BoxDecoration(
        color: AppPallete.white,
        borderRadius: BorderRadius.circular(ValuesManager.borderRadiusLarge),
        border: Border.all(color: AppPallete.borderColor),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8.r,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'المبيعات الشهرية',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: FontSize.s18,
            ),
          ),
          SizedBox(height: ValuesManager.marginMedium),
          SizedBox(
            height: 200.h,
            child: LineChart(
              _buildChartData(chartData, maxValue),
            ),
          ),
        ],
      ),
    );
  }

  LineChartData _buildChartData(OrderChartData data, double maxValue) {
    return LineChartData(
      gridData: const FlGridData(show: false),
      titlesData: FlTitlesData(
        show: true,
        rightTitles: const AxisTitles(
          sideTitles: SideTitles(showTitles: false),
        ),
        topTitles: const AxisTitles(
          sideTitles: SideTitles(showTitles: false),
        ),
        bottomTitles: AxisTitles(
          sideTitles: SideTitles(
            showTitles: true,
            reservedSize: 30.h,
            getTitlesWidget: (value, meta) {
              const style = TextStyle(
                color: AppPallete.lightGreyForText,
                fontSize: 10,
              );
              final index = value.toInt();
              if (index >= 0 && index < data.monthlySales.length) {
                return Text(data.monthlySales[index].month, style: style);
              }
              return const Text('', style: style);
            },
          ),
        ),
        leftTitles: AxisTitles(
          sideTitles: SideTitles(
            showTitles: true,
            reservedSize: 40.w,
            getTitlesWidget: (value, meta) {
              if (value % (maxValue / 5) == 0) {
                return Text(
                  '${(value / 1000).toInt()} ألف',
                  style: const TextStyle(
                    color: AppPallete.lightGreyForText,
                    fontSize: 10,
                  ),
                );
              }
              return const Text('');
            },
          ),
        ),
      ),
      borderData: FlBorderData(show: false),
      minX: 0,
      maxX: (data.monthlySales.length - 1).toDouble(),
      minY: 0,
      maxY: maxValue * 1.2,
      lineBarsData: [
        LineChartBarData(
          spots: data.monthlySales.asMap().entries.map((entry) {
            final index = entry.key;
            final item = entry.value;
            return FlSpot(index.toDouble(), item.value);
          }).toList(),
          isCurved: true,
          color: AppPallete.primaryColor,
          barWidth: 3.w,
          isStrokeCapRound: true,
          dotData: const FlDotData(show: true),
          belowBarData: BarAreaData(
            show: true,
            color: AppPallete.primaryColor.withOpacity(0.1),
          ),
        ),
      ],
    );
  }
}