
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:pharmacy_restaurant_seller/features/orders/presentation/screens/orders_list_screen.dart';
import '../../../../core/theme/app_pallete.dart';
import '../../../../core/theme/font_weight_helper.dart';
import '../../../../core/theme/values_manager.dart';

class QuickActions extends StatelessWidget {
  const QuickActions({super.key});

  @override
  Widget build(BuildContext context) {
    final actions = [
      {
        'icon': Icons.shopping_cart,
        'label': 'الطلبات',
        'color': AppPallete.primaryColor,
        'onTap': () => Navigator.of(context).push(
          MaterialPageRoute(builder: (_) => OrdersListScreen()),
        ),
      },
      {
        'icon': Icons.inventory,
        'label': 'المنتجات',
        'color': AppPallete.secondary,
        'onTap': () => _navigateToProducts(context),
      },
      {
        'icon': Icons.analytics,
        'label': 'التحليلات',
        'color': AppPallete.processing,
        'onTap': () => _navigateToAnalytics(context),
      },
      {
        'icon': Icons.store,
        'label': 'إدارة المتجر',
        'color': AppPallete.shipped,
        'onTap': () => _navigateToStoreManagement(context),
      },
    ];

    return Container(
      padding: EdgeInsets.all(ValuesManager.paddingMedium),
      decoration: BoxDecoration(
        color: AppPallete.white,
        borderRadius: BorderRadius.circular(ValuesManager.borderRadiusLarge),
        border: Border.all(color: AppPallete.borderColor),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8.r,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'إجراءات سريعة',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: FontSize.s18,
            ),
          ),
          SizedBox(height: ValuesManager.marginMedium),
          GridView.count(
            crossAxisCount: 2,
            crossAxisSpacing: ValuesManager.marginMedium,
            mainAxisSpacing: ValuesManager.marginMedium,
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            children: actions.map((action) {
              return _ActionCard(
                icon: action['icon'] as IconData,
                label: action['label'] as String,
                color: action['color'] as Color,
                onTap: action['onTap'] as VoidCallback,
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  void _navigateToOrders(BuildContext context) {
    print('الانتقال لشاشة الطلبات');
  }

  void _navigateToProducts(BuildContext context) {
    print('الانتقال لشاشة المنتجات');
  }

  void _navigateToAnalytics(BuildContext context) {
    print('الانتقال لشاشة التحليلات');
  }

  void _navigateToStoreManagement(BuildContext context) {
    print('الانتقال لإدارة المتجر');
  }
}

class _ActionCard extends StatelessWidget {
  final IconData icon;
  final String label;
  final Color color;
  final VoidCallback onTap;

  const _ActionCard({
    required this.icon,
    required this.label,
    required this.color,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.all(ValuesManager.paddingMedium),
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(ValuesManager.borderRadiusLarge),
          border: Border.all(color: color.withOpacity(0.3)),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              color: color,
              size: 24.sp,
            ),
            SizedBox(height: ValuesManager.marginSmall),
            Text(
              label,
              style: TextStyle(
                color: color,
                fontSize: FontSize.s12,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}