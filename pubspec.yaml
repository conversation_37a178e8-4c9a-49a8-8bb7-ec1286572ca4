name: pharmacy_restaurant_seller
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.7.2

dependencies:
  flutter:
    sdk: flutter

  cupertino_icons: ^1.0.8
  supabase_flutter: ^2.9.1
  flutter_riverpod: ^2.6.1
  fpdart: ^1.1.1
  internet_connection_checker_plus: ^2.7.2
  uuid: ^4.5.1
  image_picker: ^1.1.2
  dotted_border: ^3.1.0
  flutter_screenutil: ^5.9.3
  flutter_carousel_widget: ^3.1.0
  flutter_svg: ^2.2.0
  tuple: ^2.0.2
  flutter_secure_storage: ^9.2.4
  cached_network_image: ^3.4.1
  flutter_cache_manager: ^3.4.1
  sqflite: ^2.4.2
  animations: ^2.0.11
  animate_do: ^4.2.0
  flutter_custom_tabs: ^2.4.0
  google_generative_ai: ^0.4.7
  local_auth: ^2.3.0
  go_router: ^16.0.0
  http: ^1.4.0
  geolocator: ^14.0.2
  geocoding: ^4.0.0
#  uni_links: ^0.5.1
  equatable: ^2.0.7
  intl: ^0.20.2
  freezed: ^3.2.0
  freezed_annotation: ^3.1.0
  formz: ^0.8.0
  connectivity_plus: ^6.1.4
  json_annotation: ^4.9.0
  shared_preferences: ^2.5.3
  flutter_localization: ^0.3.3
  fl_chart: ^1.0.0
  shimmer: ^3.0.0
dev_dependencies:
  flutter_test:
    sdk: flutter

  flutter_lints: ^6.0.0
  build_runner: ^2.6.0
  json_serializable: ^6.10.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/svgs/
    - assets/images/btns/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
