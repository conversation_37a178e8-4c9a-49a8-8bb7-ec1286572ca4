
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shimmer/shimmer.dart';
import '../../../../core/theme/app_pallete.dart';
import '../../../../core/theme/values_manager.dart';

class DashboardLoadingShimmer extends StatelessWidget {
  const DashboardLoadingShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: AppPallete.white,
      highlightColor: AppPallete.white,
      child: Container(
        padding: EdgeInsets.all(ValuesManager.paddingMedium),
        decoration: BoxDecoration(
          color: AppPallete.white,
          borderRadius: BorderRadius.circular(ValuesManager.borderRadiusLarge),
          border: Border.all(color: AppPallete.borderColor),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              height: 24.h,
              width: 120.w,
              decoration: BoxDecoration(
                color: AppPallete.white,
                borderRadius: BorderRadius.circular(ValuesManager.borderRadius),
              ),
            ),
            SizedBox(height: ValuesManager.marginMedium),
            GridView.count(
              crossAxisCount: 2,
              crossAxisSpacing: ValuesManager.marginSmall,
              mainAxisSpacing: ValuesManager.marginSmall,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              children: List.generate(4, (index) {
                return Container(
                  padding: EdgeInsets.all(ValuesManager.paddingMedium),
                  decoration: BoxDecoration(
                    color: AppPallete.white,
                    borderRadius: BorderRadius.circular(ValuesManager.borderRadius),
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Container(
                        width: 24.w,
                        height: 24.h,
                        decoration: BoxDecoration(
                          color: AppPallete.white,
                          shape: BoxShape.circle,
                        ),
                      ),
                      SizedBox(height: ValuesManager.marginSmall),
                      Container(
                        height: 16.h,
                        width: 60.w,
                        decoration: BoxDecoration(
                          color: AppPallete.white,
                          borderRadius: BorderRadius.circular(ValuesManager.borderRadius),
                        ),
                      ),
                      SizedBox(height: ValuesManager.marginSmall / 2),
                      Container(
                        height: 12.h,
                        width: 80.w,
                        decoration: BoxDecoration(
                          color: AppPallete.white,
                          borderRadius: BorderRadius.circular(ValuesManager.borderRadius),
                        ),
                      ),
                    ],
                  ),
                );
              }),
            ),
          ],
        ),
      ),
    );
  }
}