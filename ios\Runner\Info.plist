<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <!-- معلومات التطبيق الأساسية -->
    <key>CFBundleDevelopmentRegion</key>
    <string>$(DEVELOPMENT_LANGUAGE)</string>
    <key>CFBundleDisplayName</key>
    <string>Pharmacy Restaurant Seller</string>
    <key>CFBundleExecutable</key>
    <string>$(EXECUTABLE_NAME)</string>
    <key>CFBundleIdentifier</key>
    <string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
    <key>CFBundleInfoDictionaryVersion</key>
    <string>6.0</string>
    <key>CFBundleName</key>
    <string>pharmacy_restaurant_seller</string>
    <key>CFBundlePackageType</key>
    <string>APPL</string>
    <key>CFBundleShortVersionString</key>
    <string>$(FLUTTER_BUILD_NAME)</string>
    <key>CFBundleSignature</key>
    <string>????</string>
    <key>CFBundleVersion</key>
    <string>$(FLUTTER_BUILD_NUMBER)</string>
    <key>LSRequiresIPhoneOS</key>
    <true/>
    
    <!-- إعدادات واجهة المستخدم -->
    <key>UILaunchStoryboardName</key>
    <string>LaunchScreen</string>
    <key>UIMainStoryboardFile</key>
    <string>Main</string>
    <key>UIViewControllerBasedStatusBarAppearance</key>
    <false/>
    
    <!-- دعم الاتجاهات للـ iPhone -->
    <key>UISupportedInterfaceOrientations</key>
    <array>
        <string>UIInterfaceOrientationPortrait</string>
        <string>UIInterfaceOrientationLandscapeLeft</string>
        <string>UIInterfaceOrientationLandscapeRight</string>
    </array>
    
    <!-- دعم الاتجاهات للـ iPad -->
    <key>UISupportedInterfaceOrientations~ipad</key>
    <array>
        <string>UIInterfaceOrientationPortrait</string>
        <string>UIInterfaceOrientationPortraitUpsideDown</string>
        <string>UIInterfaceOrientationLandscapeLeft</string>
        <string>UIInterfaceOrientationLandscapeRight</string>
    </array>
    
    <!-- إعداد Deep Linking -->
    <key>CFBundleURLTypes</key>
    <array>
        <!-- Custom URL Scheme للتطبيق -->
        <dict>
            <key>CFBundleURLName</key>
            <string>pharmacy_restaurant_seller.deeplink</string>
            <key>CFBundleURLSchemes</key>
            <array>
                <string>myapp</string>
            </array>
        </dict>
        
        <!-- Universal Links للـ Supabase -->
        <dict>
            <key>CFBundleURLName</key>
            <string>pharmacy_restaurant_seller.supabase</string>
            <key>CFBundleURLSchemes</key>
            <array>
                <string>https</string>
            </array>
        </dict>
    </array>
    
    <!-- إعدادات الأداء -->
    <key>CADisableMinimumFrameDurationOnPhone</key>
    <true/>
    <key>UIApplicationSupportsIndirectInputEvents</key>
    <true/>
    
    <!-- الصلاحيات الأساسية -->
    <key>NSAppTransportSecurity</key>
    <dict>
        <key>NSAllowsArbitraryLoads</key>
        <true/>
    </dict>
    
    <!-- إعدادات الخصوصية -->
    <key>NSCameraUsageDescription</key>
    <string>This app needs access to camera to take photos</string>
    <key>NSPhotoLibraryUsageDescription</key>
    <string>This app needs access to photo library to select images</string>
    
    <!-- إعدادات الشبكة -->
    <key>NSNetworkVolumesUsageDescription</key>
    <string>This app needs network access to sync data</string>
    
</dict>
</plist>