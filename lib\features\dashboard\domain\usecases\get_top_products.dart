import 'package:fpdart/fpdart.dart';

import '../entitise/top_product.dart';
import '../repo/dashboard_repo.dart';

class GetTopProducts {
  final DashboardRepository repository;

  GetTopProducts(this.repository);

  Future<Either<Exception, List<TopProduct>>> call() async {
    try {
      final Either<Exception, List<TopProduct>> products = await repository.getTopProducts();
      return products;
    } on Exception catch (e) {
      return Left(e);
    }
  }
}
