
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/theme/app_pallete.dart';
import '../../../../core/theme/font_weight_helper.dart';
import '../../../../core/theme/values_manager.dart';
import '../riverpods/dashboard_notifier.dart';
import '../widgets/chart_widget.dart';
import '../widgets/quick_actions.dart';
import '../widgets/stats_card.dart';
import '../widgets/top_products.dart';

class DashboardScreen extends ConsumerWidget {
  const DashboardScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final dashboardState = ref.watch(dashboardNotifierProvider);

    return Scaffold(
      appBar: _buildAppBar(context),
      body: RefreshIndicator(
        onRefresh: () => _refreshDashboard(ref),
        child: SingleChildScrollView(
          padding: EdgeInsets.all(ValuesManager.paddingMedium),
          child: Column(
            children: [
              DashboardStatsCard(
                stats: dashboardState.stats,
                isLoading: dashboardState.isLoadingStats,
                error: dashboardState.statsError,
                onRetry: () => ref.read(dashboardNotifierProvider.notifier).retryStats(),
              ),
              SizedBox(height: ValuesManager.marginMedium),
              OrdersChartWidget(
                chartData: dashboardState.chartData,
                isLoading: dashboardState.isLoadingChart,
                error: dashboardState.chartError,
                onRetry: () => ref.read(dashboardNotifierProvider.notifier).retryChart(),
              ),
              SizedBox(height: ValuesManager.marginMedium),
              TopProductsWidget(
                products: dashboardState.topProducts,
                isLoading: dashboardState.isLoadingProducts,
                error: dashboardState.productsError,
                onRetry: () => ref.read(dashboardNotifierProvider.notifier).retryProducts(),
              ),
              SizedBox(height: ValuesManager.marginMedium),
              const QuickActions(),
            ],
          ),
        ),
      ),
    );
  }

  AppBar _buildAppBar(BuildContext context) {
    return AppBar(
      title: const Text(
        'لوحة التحكم',
        style: TextStyle(
          fontSize: FontSize.s20,
          fontWeight: FontWeight.bold,
        ),
      ),
      centerTitle: true,
      actions: [
        IconButton(
          onPressed: () {
            // فتح الإشعارات
            _openNotifications(context);
          },
          icon: const Icon(Icons.notifications_none),
        ),
        IconButton(
          onPressed: () {
            // فتح الإعدادات
            _openSettings(context);
          },
          icon: const Icon(Icons.settings),
        ),
      ],
      backgroundColor: AppPallete.white,
      elevation: 0,
    );
  }

  Future<void> _refreshDashboard(WidgetRef ref) async {
    await ref.read(dashboardNotifierProvider.notifier).refresh();
  }

  void _openNotifications(BuildContext context) {
    // فتح شاشة الإشعارات
    print('فتح الإشعارات');
  }

  void _openSettings(BuildContext context) {
    // فتح الإعدادات
    print('فتح الإعدادات');
  }
}