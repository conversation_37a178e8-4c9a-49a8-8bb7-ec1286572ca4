
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/theme/app_pallete.dart';
import '../../../../core/theme/font_weight_helper.dart';
import '../../../../core/theme/values_manager.dart';
import '../../domain/entitise/top_product.dart';

class TopProductsWidget extends StatelessWidget {
  final List<TopProduct>? products;
  final bool isLoading;
  final String? error;
  final VoidCallback? onRetry;

  const TopProductsWidget({
    super.key,
    required this.products,
    required this.isLoading,
    this.error,
    this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    if (isLoading && (products == null || products!.isEmpty)) {
      return const _ProductsSkeleton();
    }

    if (error != null) {
      return _ErrorState(
        message: error!,
        onRetry: onRetry,
      );
    }

    if (products == null || products!.isEmpty) {
      return _EmptyState(
        onRetry: onRetry,
      );
    }

    return _ProductsContent(products: products!);
  }
}

class _ProductsContent extends StatelessWidget {
  final List<TopProduct> products;

  const _ProductsContent({required this.products});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(ValuesManager.paddingMedium),
      decoration: BoxDecoration(
        color: AppPallete.white,
        borderRadius: BorderRadius.circular(ValuesManager.borderRadiusLarge),
        border: Border.all(color: AppPallete.borderColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'المنتجات الأكثر مبيعاً',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: FontSize.s18,
            ),
          ),
          SizedBox(height: ValuesManager.marginMedium),
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: products.length,
            separatorBuilder: (_, __) => Divider(height: ValuesManager.marginMedium),
            itemBuilder: (context, index) {
              final product = products[index];
              return _ProductItem(
                product: product,
                index: index,
              );
            },
          ),
        ],
      ),
    );
  }
}

class _ProductItem extends StatelessWidget {
  final TopProduct product;
  final int index;

  const _ProductItem({
    required this.product,
    required this.index,
  });

  @override
  Widget build(BuildContext context) {
    final (color, icon) = _getRankInfo(index);

    return Row(
      children: [
        Container(
          width: 32.w,
          height: 32.h,
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            shape: BoxShape.circle,
            border: Border.all(color: color.withOpacity(0.3)),
          ),
          child: Center(
            child: Icon(
              icon,
              color: color,
              size: 16.sp,
            ),
          ),
        ),
        SizedBox(width: ValuesManager.marginMedium),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                product.name,
                style: const TextStyle(
                  fontWeight: FontWeight.w500,
                  fontSize: FontSize.s14,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              SizedBox(height: ValuesManager.marginSmall / 2),
              Text(
                '${product.sales} مبيعات • ${product.revenue.toStringAsFixed(0)} ر.س',
                style: const TextStyle(
                  color: AppPallete.lightGreyForText,
                  fontSize: FontSize.s12,
                ),
              ),
            ],
          ),
        ),
        Text(
          '#${product.rank}',
          style: TextStyle(
            color: color,
            fontWeight: FontWeight.bold,
            fontSize: FontSize.s12,
          ),
        ),
      ],
    );
  }

  (Color, IconData) _getRankInfo(int index) {
    switch (index) {
      case 0:
        return (AppPallete.primaryColor, Icons.workspace_premium);
      case 1:
        return (AppPallete.secondary, Icons.workspace_premium_outlined);
      case 2:
        return (AppPallete.processing, Icons.workspace_premium_outlined);
      default:
        return (AppPallete.lightGreyForText, Icons.workspace_premium_outlined);
    }
  }
}

class _ProductsSkeleton extends StatelessWidget {
  const _ProductsSkeleton();

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(ValuesManager.paddingMedium),
      decoration: BoxDecoration(
        color: AppPallete.white,
        borderRadius: BorderRadius.circular(ValuesManager.borderRadiusLarge),
        border: Border.all(color: AppPallete.borderColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'المنتجات الأكثر مبيعاً',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: FontSize.s18,
            ),
          ),
          SizedBox(height: ValuesManager.marginMedium),
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: 5,
            separatorBuilder: (_, __) => Divider(height: ValuesManager.marginMedium),
            itemBuilder: (context, index) {
              return Container(
                height: 48.h,
                decoration: BoxDecoration(
                  color: AppPallete.background,
                  borderRadius: BorderRadius.circular(ValuesManager.borderRadius),
                ),
                child: const Center(child: CircularProgressIndicator()),
              );
            },
          ),
        ],
      ),
    );
  }
}

class _ErrorState extends StatelessWidget {
  final String message;
  final VoidCallback? onRetry;

  const _ErrorState({
    required this.message,
    this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(ValuesManager.paddingMedium),
      decoration: BoxDecoration(
        color: AppPallete.white,
        borderRadius: BorderRadius.circular(ValuesManager.borderRadiusLarge),
        border: Border.all(color: AppPallete.borderColor),
      ),
      child: Column(
        children: [
          const Icon(
            Icons.error_outline,
            color: AppPallete.redColor,
            size: 48,
          ),
          SizedBox(height: ValuesManager.marginMedium),
          const Text(
            'حدث خطأ',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: FontSize.s18,
            ),
          ),
          SizedBox(height: ValuesManager.marginSmall),
          Text(
            message,
            style: const TextStyle(
              color: AppPallete.lightGreyForText,
              fontSize: FontSize.s14,
            ),
            textAlign: TextAlign.center,
          ),
          if (onRetry != null) ...[
            SizedBox(height: ValuesManager.marginLarge),
            ElevatedButton(
              onPressed: onRetry,
              child: const Text('إعادة المحاولة'),
            ),
          ],
        ],
      ),
    );
  }
}

class _EmptyState extends StatelessWidget {
  final VoidCallback? onRetry;

  const _EmptyState({this.onRetry});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(ValuesManager.paddingMedium),
      decoration: BoxDecoration(
        color: AppPallete.white,
        borderRadius: BorderRadius.circular(ValuesManager.borderRadiusLarge),
        border: Border.all(color: AppPallete.borderColor),
      ),
      child: Column(
        children: [
          Icon(
            Icons.inventory_outlined,
            color: AppPallete.lightGreyForText.withOpacity(0.3),
            size: 48,
          ),
          SizedBox(height: ValuesManager.marginMedium),
          const Text(
            'لا توجد منتجات',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: FontSize.s18,
            ),
          ),
          SizedBox(height: ValuesManager.marginSmall),
          const Text(
            'سيتم عرض المنتجات الأكثر مبيعاً هنا عند استلام الطلبات',
            style: TextStyle(
              color: AppPallete.lightGreyForText,
              fontSize: FontSize.s14,
            ),
            textAlign: TextAlign.center,
          ),
          if (onRetry != null) ...[
            SizedBox(height: ValuesManager.marginLarge),
            ElevatedButton(
              onPressed: onRetry,
              child: const Text('إعادة المحاولة'),
            ),
          ],
        ],
      ),
    );
  }
}