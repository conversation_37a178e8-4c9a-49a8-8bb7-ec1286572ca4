
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/theme/app_pallete.dart';
import '../../../../core/theme/font_weight_helper.dart';
import '../../../../core/theme/values_manager.dart';
import '../../domain/entitise/dashboard_stats.dart';
import 'dashboard_empty_state.dart';
import 'dashboard_loading_shimmer.dart';

class DashboardStatsCard extends StatelessWidget {
  final DashboardStats? stats;
  final bool isLoading;
  final String? error;
  final VoidCallback? onRetry;

  const DashboardStatsCard({
    super.key,
    this.stats,
    required this.isLoading,
    this.error,
    this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    if (isLoading && stats == null) {
      return const DashboardLoadingShimmer();
    }

    if (error != null) {
      return DashboardEmptyState(
        title: 'حدث خطأ',
        message: error!,
        icon: Icons.error_outline,
        onRetry: onRetry,
      );
    }

    if (stats == null) {
      return DashboardEmptyState(
        title: 'لا توجد بيانات',
        message: 'سيتم عرض الإحصائيات هنا عند استلام الطلبات',
        icon: Icons.bar_chart,
        onRetry: onRetry,
      );
    }

    return _StatsContent(stats: stats!);
  }
}

class _StatsContent extends StatelessWidget {
  final DashboardStats stats;

  const _StatsContent({required this.stats});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(ValuesManager.paddingMedium),
      decoration: BoxDecoration(
        color: AppPallete.white,
        borderRadius: BorderRadius.circular(ValuesManager.borderRadiusLarge),
        border: Border.all(color: AppPallete.borderColor),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8.r,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          const Text(
            'الإحصائيات',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: FontSize.s18,
            ),
          ),
          SizedBox(height: ValuesManager.marginMedium),
          _buildStatsGrid(stats),
        ],
      ),
    );
  }

  Widget _buildStatsGrid(DashboardStats stats) {
    final statsList = [
      {
        'title': 'إجمالي المبيعات',
        'value': '${stats.totalSales.toStringAsFixed(2)} ر.س',
        'icon': Icons.attach_money,
        'color': AppPallete.primaryColor,
      },
      {
        'title': 'عدد الطلبات',
        'value': '${stats.totalOrders}',
        'icon': Icons.shopping_cart,
        'color': AppPallete.secondary,
      },
      {
        'title': 'متوسط الطلب',
        'value': '${stats.averageOrderValue.toStringAsFixed(2)} ر.س',
        'icon': Icons.bar_chart,
        'color': AppPallete.processing,
      },
      {
        'title': 'الطلبات المعلقة',
        'value': '${stats.pendingOrders + stats.processingOrders}',
        'icon': Icons.access_time,
        'color': AppPallete.pending,
      },
    ];

    return GridView.count(
      crossAxisCount: 2,
      crossAxisSpacing: ValuesManager.marginSmall,
      mainAxisSpacing: ValuesManager.marginSmall,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      children: statsList.map((stat) {
        return _StatItem(
          title: stat['title'] as String,
          value: stat['value'] as String,
          icon: stat['icon'] as IconData,
          color: stat['color'] as Color,
        );
      }).toList(),
    );
  }
}

class _StatItem extends StatelessWidget {
  final String title;
  final String value;
  final IconData icon;
  final Color color;

  const _StatItem({
    required this.title,
    required this.value,
    required this.icon,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(ValuesManager.paddingMedium),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(ValuesManager.borderRadius),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            color: color,
            size: 24.sp,
          ),
          SizedBox(height: ValuesManager.marginSmall),
          Text(
            value,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: FontSize.s16,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: ValuesManager.marginSmall / 2),
          Text(
            title,
            style: TextStyle(
              color: AppPallete.lightGreyForText,
              fontSize: FontSize.s12,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}