import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class Gap {
  static SizedBox get h20 => SizedBox(height: 20.h);

  static SizedBox get h16 => SizedBox(height: 16.h);

  static SizedBox get h12 => SizedBox(height: 12.h);

  static SizedBox get h8 => SizedBox(height: 8.h);

  static SizedBox get h4 => SizedBox(height: 4.h);

  static SizedBox get h2 => SizedBox(height: 2.h);

  static SizedBox get h1 => SizedBox(height: 1.h);

  static SizedBox get w4 => SizedBox(width: 4.w);

  static SizedBox get w20 => SizedBox(width: 20.w);

  static SizedBox get w16 => SizedBox(width: 16.w);

  static SizedBox get w12 => SizedBox(width: 12.w);

  static SizedBox get w8 => SizedBox(width: 8.w);
}
