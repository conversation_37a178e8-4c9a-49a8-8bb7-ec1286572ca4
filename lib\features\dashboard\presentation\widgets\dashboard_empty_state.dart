
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/theme/app_pallete.dart';
import '../../../../core/theme/font_weight_helper.dart';
import '../../../../core/theme/values_manager.dart';

class DashboardEmptyState extends StatelessWidget {
  final String title;
  final String message;
  final IconData icon;
  final VoidCallback? onRetry;

  const DashboardEmptyState({
    Key? key,
    required this.title,
    required this.message,
    required this.icon,
    this.onRetry,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Center(
      child: SingleChildScrollView(
        padding: EdgeInsets.all(ValuesManager.paddingLarge),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 64.sp,
              color: AppPallete.lightGreyForText.withOpacity(0.3),
            ),
            SizedBox(height: ValuesManager.marginMedium),
            Text(
              title,
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: FontSize.s20,
                color: AppPallete.lightGreyForText,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: ValuesManager.marginSmall),
            Text(
              message,
              style: const TextStyle(
                color: AppPallete.lightGreyForText,
                fontSize: FontSize.s16,
              ),
              textAlign: TextAlign.center,
            ),
            if (onRetry != null) ...[
              SizedBox(height: ValuesManager.marginLarge),
              ElevatedButton(
                onPressed: onRetry,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppPallete.primaryColor,
                  padding: EdgeInsets.symmetric(
                    horizontal: ValuesManager.paddingLarge,
                    vertical: ValuesManager.paddingMedium,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(ValuesManager.borderRadius),
                  ),
                ),
                child: const Text('إعادة المحاولة'),
              ),
            ],
          ],
        ),
      ),
    );
  }
}