import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'app_pallete.dart';
import 'font_weight_helper.dart';

class TextStyles {
  static TextStyle blinker16semiBoldBlack = TextStyle(
    fontSize: 16.h,
    fontWeight: FontWeightHelper.semiBold,
    fontFamily: "Blinker",
    color: AppPallete.blackForText,
  );

  static TextStyle blinker5regularblack = TextStyle(
    fontSize: 7.h,
    fontWeight: FontWeightHelper.regular,
    fontFamily: "Blinker",
    color: AppPallete.blackForText,
  );

  static TextStyle blinker14regularwhite = TextStyle(
    fontSize: 8.h,
    fontWeight: FontWeightHelper.regular,
    fontFamily: "Blinker",
    color: const Color(0xFFF68B3B),
  );

  static TextStyle blinker24SemiBoldLightGrey = TextStyle(
    fontSize: 24.h,
    fontWeight: FontWeightHelper.semiBold,
    fontFamily: "Blinker",
    color: AppPallete.lightGreyForText,
  );

  static TextStyle blinker20SemiBoldwhite = TextStyle(
    fontSize: 20.h,
    fontWeight: FontWeightHelper.semiBold,
    fontFamily: "Blinker",
    color: AppPallete.white,
  );

  static TextStyle blinker20SemiBoldBlack = TextStyle(
    fontSize: 20.h,
    fontWeight: FontWeightHelper.semiBold,
    fontFamily: "Blinker",
    color: AppPallete.blackForText,
  );

  static TextStyle blinker16RegularLightBlue = TextStyle(
    fontSize: 16.h,
    fontWeight: FontWeightHelper.regular,
    fontFamily: "Blinker",
    color: AppPallete.lightBlueForText,
  );

  static TextStyle blinker14RegularDarkGrey = TextStyle(
    fontSize: 14.h,
    fontWeight: FontWeightHelper.regular,
    fontFamily: "Blinker",
    color: AppPallete.darkGreyForText,
  );

  static TextStyle blinker20SemiBoldLightBlack = TextStyle(
    fontSize: 20.h,
    fontWeight: FontWeightHelper.semiBold,
    fontFamily: "Blinker",
    color: AppPallete.lightBlack,
  );

  static TextStyle Inter18SemiBoldlightBlack = TextStyle(
    fontSize: 18.h,
    fontWeight: FontWeightHelper.semiBold,
    fontFamily: "Inter",
    color: AppPallete.lightBlack,
  );

  static TextStyle Roboto20mediumBlack = TextStyle(
    fontSize: 20.h,
    fontWeight: FontWeightHelper.medium,
    fontFamily: "Roboto",
    color: AppPallete.blackForText,
  );

  static TextStyle Roboto14mediumblackForText = TextStyle(
    fontSize: 14.h,
    fontWeight: FontWeightHelper.medium,
    fontFamily: "Roboto",
    color: AppPallete.blackForText,
  );

  static TextStyle Roboto15regularlightBlack = TextStyle(
    fontSize: 15.h,
    fontWeight: FontWeightHelper.regular,
    fontFamily: "Roboto",
    color: AppPallete.lightBlack,
  );

  static TextStyle Blinker20semiBoldBlack = TextStyle(
    fontSize: 20.h,
    fontWeight: FontWeightHelper.semiBold,
    fontFamily: "Blinker",
    color: AppPallete.blackForText,
  );

  static TextStyle Blinker16regularlightBlack = TextStyle(
    fontSize: 16.h,
    fontWeight: FontWeightHelper.regular,
    fontFamily: "Blinker",
    color: AppPallete.lightBlack,
  );

  static TextStyle Raleway14mediuBlack = TextStyle(
    fontSize: 14.h,
    fontWeight: FontWeightHelper.medium,
    fontFamily: "Raleway",
    color: AppPallete.blackForText,
  );

  static TextStyle Blinker12regularlightBlack = TextStyle(
    fontSize: 12.h,
    fontWeight: FontWeightHelper.regular,
    fontFamily: "Blinker",
    color: AppPallete.lightBlack,
  );

  static TextStyle Blinker14semiBoldBlack = TextStyle(
    fontSize: 12.h,
    fontWeight: FontWeightHelper.semiBold,
    fontFamily: "Blinker",
    color: AppPallete.blackForText,
  );

  static TextStyle Lato16extraBoldBlack = TextStyle(
    fontSize: 16.h,
    fontWeight: FontWeightHelper.extraBold,
    fontFamily: "Lato",
    color: AppPallete.blackForText,
  );

  static TextStyle Lato14extraBoldBlack = TextStyle(
    fontSize: 14.h,
    fontWeight: FontWeightHelper.extraBold,
    fontFamily: "Lato",
    color: AppPallete.blackForText,
  );

  static TextStyle Lato12extraBoldBlack = TextStyle(
    fontSize: 12.h,
    fontWeight: FontWeightHelper.extraBold,
    fontFamily: "Lato",
    color: AppPallete.blackForText,
  );

  static TextStyle Lato16regularBlack = TextStyle(
    fontSize: 16.h,
    fontWeight: FontWeightHelper.regular,
    fontFamily: "Lato",
    color: AppPallete.blackForText,
  );

  static TextStyle DMSans20mediumlightBlack = TextStyle(
    fontSize: 20.h,
    fontWeight: FontWeightHelper.medium,
    fontFamily: "DM Sans",
    color: AppPallete.lightBlack,
  );

  static TextStyle Blinker18semiBoldBlack = TextStyle(
    fontSize: 18.h,
    fontWeight: FontWeightHelper.semiBold,
    fontFamily: "Blinker",
    color: AppPallete.blackForText,
  );

  static TextStyle Blinker24boldBlack = TextStyle(
    fontSize: 18.h,
    fontWeight: FontWeightHelper.bold,
    fontFamily: "Blinker",
    color: AppPallete.blackForText,
  );

  static TextStyle Blinker16regularorangeColor = TextStyle(
    fontSize: 16.h,
    fontWeight: FontWeightHelper.regular,
    fontFamily: "Blinker",
    color: AppPallete.orangeColor,
  );

  static TextStyle Blinker16regularlightOrange = TextStyle(
    fontSize: 16.h,
    fontWeight: FontWeightHelper.regular,
    fontFamily: "Blinker",
    color: AppPallete.lightOrange,
  );

  static TextStyle Blinker14regular = TextStyle(
    fontSize: 14.h,
    fontWeight: FontWeightHelper.regular,
    fontFamily: "Blinker",
  );

  static TextStyle Roboto14mediumBlack = TextStyle(
    fontSize: 14.h,
    fontWeight: FontWeightHelper.medium,
    fontFamily: "Roboto",
    color: AppPallete.blackForText,
  );

  static TextStyle blinker16SemiBoldWhite = TextStyle(
    fontSize: 16.h,
    fontWeight: FontWeightHelper.semiBold,
    fontFamily: "Blinker",
    color: AppPallete.white,
  );

  static TextStyle Inter28SemiBoldBlack = TextStyle(
    fontSize: 28.h,
    fontWeight: FontWeightHelper.semiBold,
    fontFamily: "Inter",
    color: AppPallete.blackForText,
  );

  static TextStyle Poppins14regularBlue = TextStyle(
    fontSize: 14.h,
    fontWeight: FontWeightHelper.regular,
    fontFamily: "Poppins",
    color: AppPallete.lightBlueForText,
  );

  static TextStyle Inter19semiBoldBlack = TextStyle(
    fontSize: 19.h,
    fontWeight: FontWeightHelper.semiBold,
    fontFamily: "Inter",
    color: AppPallete.blackForText,
  );

  static TextStyle Inter12regularlightBlack = TextStyle(
    fontSize: 12.h,
    fontWeight: FontWeightHelper.regular,
    fontFamily: "Inter",
    color: AppPallete.lightBlack,
  );

  static TextStyle Montserrat17mediumBlack = TextStyle(
    fontSize: 17.h,
    fontWeight: FontWeightHelper.medium,
    fontFamily: "Montserrat",
    color: AppPallete.blackForText,
  );

  static TextStyle Montserrat16regularBlack = TextStyle(
    fontSize: 16.h,
    fontWeight: FontWeightHelper.regular,
    fontFamily: "Montserrat",
    color: AppPallete.blackForText,
  );

  static TextStyle Montserrat16semiBoldBlack = TextStyle(
    fontSize: 16.h,
    fontWeight: FontWeightHelper.semiBold,
    fontFamily: "Montserrat",
    color: AppPallete.blackForText,
  );

  static TextStyle Montserrat14semiBoldbinkForText = TextStyle(
    fontSize: 14.h,
    fontWeight: FontWeightHelper.semiBold,
    fontFamily: "Montserrat",
    color: AppPallete.binkForText,
  );

  static TextStyle Inter17mediumBlack = TextStyle(
    fontSize: 17.h,
    fontWeight: FontWeightHelper.medium,
    fontFamily: "Inter",
    color: AppPallete.blackForText,
  );

  static TextStyle Inter15regularBlack = TextStyle(
    fontSize: 15.h,
    fontWeight: FontWeightHelper.regular,
    fontFamily: "Inter",
    color: AppPallete.blackForText,
  );

  static TextStyle Inter15mediumbinkForText = TextStyle(
    fontSize: 15.h,
    fontWeight: FontWeightHelper.medium,
    fontFamily: "Inter",
    color: AppPallete.binkForText,
  );

  static TextStyle Lato28boldlightBlack = TextStyle(
    fontSize: 28.h,
    fontWeight: FontWeightHelper.bold,
    fontFamily: "Lato",
    color: AppPallete.lightBlack,
  );

  static TextStyle Lato20boldBlack = TextStyle(
    fontSize: 20.h,
    fontWeight: FontWeightHelper.bold,
    fontFamily: "Lato",
    color: AppPallete.blackForText,
  );

  static TextStyle Lato16boldBlack = TextStyle(
    fontSize: 16.h,
    fontWeight: FontWeightHelper.bold,
    fontFamily: "Lato",
    color: AppPallete.blackForText,
  );

  static TextStyle Lato12extraboldBlack = TextStyle(
    fontSize: 12.h,
    fontWeight: FontWeightHelper.extraBold,
    fontFamily: "Lato",
    color: AppPallete.blackForText,
  );

  static TextStyle Lato17regularBlack = TextStyle(
    fontSize: 17.h,
    fontWeight: FontWeightHelper.regular,
    fontFamily: "Lato",
    color: AppPallete.blackForText,
  );

  static TextStyle Lato16regularlightBlack = TextStyle(
    fontSize: 16.h,
    fontWeight: FontWeightHelper.regular,
    fontFamily: "Lato",
    color: AppPallete.lightBlack,
  );

  static TextStyle Lato16mediumlightBlack = TextStyle(
    fontSize: 16.h,
    fontWeight: FontWeightHelper.medium,
    fontFamily: "Lato",
    color: AppPallete.lightBlack,
  );

  static TextStyle Lato16boldlightBlack = TextStyle(
    fontSize: 16.h,
    fontWeight: FontWeightHelper.bold,
    fontFamily: "Lato",
    color: AppPallete.lightBlack,
  );

  static TextStyle Roboto12regularlightGrey = TextStyle(
    fontSize: 12.h,
    fontWeight: FontWeightHelper.regular,
    fontFamily: "Roboto",
    color: AppPallete.lightGrey,
  );

  static TextStyle blinker14Boldwhite = TextStyle(
    fontSize: 14.h,
    fontWeight: FontWeightHelper.bold,
    fontFamily: "Blinker",
    color: AppPallete.white,
  );

  static TextStyle blinker12regularGreen = TextStyle(
    fontSize: 12.h,
    fontWeight: FontWeightHelper.regular,
    fontFamily: "Blinker",
    color: AppPallete.greenColor,
  );

  static TextStyle Blinker16semiBoldBlack = TextStyle(
    fontSize: 16.h,
    fontWeight: FontWeightHelper.semiBold,
    fontFamily: "Blinker",
    color: AppPallete.blackForText,
  );

  static TextStyle Blinker24semiBoldBlack = TextStyle(
    fontSize: 24.h,
    fontWeight: FontWeightHelper.semiBold,
    fontFamily: "Blinker",
    color: AppPallete.blackForText,
  );

  static TextStyle Blinker12regularBlack = TextStyle(
    fontSize: 12.h,
    fontWeight: FontWeightHelper.regular,
    fontFamily: "Blinker",
    color: AppPallete.blackForText,
  );
}
